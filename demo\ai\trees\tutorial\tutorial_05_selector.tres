[gd_resource type="BehaviorTree" load_steps=26 format=3 uid="uid://bf4r652fv5kwi"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_3ed74"]
[ext_resource type="Script" uid="uid://bicxffqmm7ek" path="res://demo/ai/tasks/select_random_nearby_pos.gd" id="1_rvh1c"]
[ext_resource type="Script" uid="uid://b7v2utjmtge0x" path="res://demo/ai/tasks/in_range.gd" id="2_jfsyt"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="2_nnswb"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="2_t62a0"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_ewfwq"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BTCondition" id="BTCondition_x0uu7"]
script = ExtResource("2_jfsyt")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_icf24"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_iiei3"]
animation_player = SubResource("BBNode_icf24")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_824oc"]
script = ExtResource("1_rvh1c")
range_min = 700.0
range_max = 800.0
position_var = &"pos"

[sub_resource type="BTAction" id="BTAction_y1you"]
script = ExtResource("2_t62a0")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BBNode" id="BBNode_ayt56"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_l1tdg"]
animation_player = SubResource("BBNode_ayt56")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_hh8ys"]
duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_cgwor"]
children = [SubResource("BTCondition_x0uu7"), SubResource("BTPlayAnimation_iiei3"), SubResource("BTAction_824oc"), SubResource("BTAction_y1you"), SubResource("BTPlayAnimation_l1tdg"), SubResource("BTWait_hh8ys")]

[sub_resource type="BTCondition" id="BTCondition_d6aub"]
script = ExtResource("2_jfsyt")
distance_min = 200.0
distance_max = 10000.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_6d0yy"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_wsspf"]
animation_player = SubResource("BBNode_6d0yy")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_wc11r"]
script = ExtResource("2_nnswb")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BBNode" id="BBNode_aw5jj"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_3aihc"]
animation_player = SubResource("BBNode_aw5jj")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_1o163"]
duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_vx4uy"]
children = [SubResource("BTCondition_d6aub"), SubResource("BTPlayAnimation_wsspf"), SubResource("BTAction_wc11r"), SubResource("BTPlayAnimation_3aihc"), SubResource("BTWait_1o163")]

[sub_resource type="BTAction" id="BTAction_2murg"]
script = ExtResource("1_3ed74")
group = &"player"
output_var = &"target"

[sub_resource type="BTSelector" id="BTSelector_ddhoo"]
children = [SubResource("BTSequence_cgwor"), SubResource("BTSequence_vx4uy"), SubResource("BTAction_2murg")]

[resource]
description = "The [comp]Selector[/comp] is another essential composite task. It executes its child tasks sequentially, from first to last, until one of them returns [SUCCESS] or all of them result in [FAILURE]. In other words, when a child task results in [FAILURE], it moves on to the next one until it finds the one that returns [SUCCESS]. Once a child task results in [SUCCESS], the Selector stops and also returns [SUCCESS].

To understand the [comp]Selector[/comp], it helps to see it as the opposite of the [comp]Sequence[/comp]. While the Sequence continues iterating over children as long as they return [SUCCESS], the Selector does the same but as long as they return [FAILURE]. The purpose of the Selector is to find a child that succeeds. It's often useful to place higher priority tasks at the top so that the Selector attempts them first.

[comp]Selector[/comp] is frequently used with [comp]Sequence[/comp] and [con]Condition[/con] tasks to control the behavior tree's flow. A condition task would prevent a Sequence from proceeding if it evaluates to [FAILURE], and thus the Selector would try the next child.

In our example, the [con]InRange[/con] condition checks how far Junior is from the target. When Junior is farther than 200 pixels from the target, he'll attempt to approach it. If he is closer than 200 pixels from the target, he would try to walk away. Thanks to the Selector, only one of these behaviors will be performed at a time, based on the result of the [con]InRange[/con] check. Junior seems to have a dilemma — unable to decide whether to stay or go."
blackboard_plan = SubResource("BlackboardPlan_ewfwq")
root_task = SubResource("BTSelector_ddhoo")
