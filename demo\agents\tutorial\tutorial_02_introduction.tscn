[gd_scene load_steps=5 format=3 uid="uid://cw3yth564nhrw"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_lia2k"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_4x2l4"]
[ext_resource type="BehaviorTree" uid="uid://b1i0xo0o676va" path="res://demo/ai/trees/tutorial/tutorial_02_introduction.tres" id="3_3esuy"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialIntro" instance=ExtResource("1_lia2k")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_4x2l4")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_4x2l4")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_4x2l4")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_4x2l4")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_4x2l4")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_4x2l4")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_3esuy")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
