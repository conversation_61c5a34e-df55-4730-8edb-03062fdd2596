[gd_resource type="BehaviorTree" load_steps=52 format=3 uid="uid://dp0cglcytwcj5"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_o5ahw"]
[ext_resource type="Script" uid="uid://ct71h72pech3b" path="res://demo/ai/tasks/select_flanking_pos.gd" id="2_er25a"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="3_ed1xo"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="4_128ei"]
[ext_resource type="Script" uid="uid://b7v2utjmtge0x" path="res://demo/ai/tasks/in_range.gd" id="5_er18b"]
[ext_resource type="Script" uid="uid://bi5e8366xi5s5" path="res://demo/ai/tasks/back_away.gd" id="6_1urfq"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="7_ekws5"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_ewfwq"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BTAction" id="BTAction_2murg"]
script = ExtResource("1_o5ahw")
group = &"player"
output_var = &"target"

[sub_resource type="BTRunLimit" id="BTRunLimit_60b8b"]
children = [SubResource("BTAction_2murg")]

[sub_resource type="BTCondition" id="BTCondition_m15aa"]
script = ExtResource("5_er18b")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BTAction" id="BTAction_oc76s"]
script = ExtResource("4_128ei")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_6d0yy"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_wsspf"]
await_completion = 5.0
animation_player = SubResource("BBNode_6d0yy")
animation_name = &"attack_1"

[sub_resource type="BBNode" id="BBNode_w45kn"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_d2lad"]
await_completion = 5.0
animation_player = SubResource("BBNode_w45kn")
animation_name = &"attack_2"

[sub_resource type="BTSequence" id="BTSequence_e0f8v"]
children = [SubResource("BTCondition_m15aa"), SubResource("BTAction_oc76s"), SubResource("BTPlayAnimation_wsspf"), SubResource("BTPlayAnimation_d2lad")]

[sub_resource type="BTCooldown" id="BTCooldown_3tvjt"]
children = [SubResource("BTSequence_e0f8v")]
duration = 5.0

[sub_resource type="BTCondition" id="BTCondition_x0uu7"]
script = ExtResource("5_er18b")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_wksgd"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_umlwj"]
animation_player = SubResource("BBNode_wksgd")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_6q0k4"]
script = ExtResource("6_1urfq")
speed_var = &"speed"
max_angle_deviation = 0.7

[sub_resource type="BTTimeLimit" id="BTTimeLimit_6eii7"]
children = [SubResource("BTAction_6q0k4")]
time_limit = 2.0

[sub_resource type="BTAlwaysSucceed" id="BTAlwaysSucceed_ieord"]
children = [SubResource("BTTimeLimit_6eii7")]

[sub_resource type="BBNode" id="BBNode_ayt56"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_l1tdg"]
animation_player = SubResource("BBNode_ayt56")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_hh8ys"]
duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_cgwor"]
children = [SubResource("BTCondition_x0uu7"), SubResource("BTPlayAnimation_umlwj"), SubResource("BTAlwaysSucceed_ieord"), SubResource("BTPlayAnimation_l1tdg"), SubResource("BTWait_hh8ys")]

[sub_resource type="BTAction" id="BTAction_n0rxm"]
script = ExtResource("2_er25a")
target_var = &"target"
flank_side = 0
range_min = 400
range_max = 500
position_var = &"pos"

[sub_resource type="BBNode" id="BBNode_icf24"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_iiei3"]
animation_player = SubResource("BBNode_icf24")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_g2up4"]
script = ExtResource("3_ed1xo")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTAction" id="BTAction_d5lkr"]
script = ExtResource("4_128ei")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_h4k80"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_40yja"]
animation_player = SubResource("BBNode_h4k80")
animation_name = &"throw_prepare"

[sub_resource type="BTWait" id="BTWait_2dc1v"]

[sub_resource type="BBNode" id="BBNode_slipn"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qnpjq"]
await_completion = 5.0
animation_player = SubResource("BBNode_slipn")
animation_name = &"throw"

[sub_resource type="BBNode" id="BBNode_qaqnn"]
resource_name = "."
saved_value = NodePath(".")

[sub_resource type="BTCallMethod" id="BTCallMethod_yd0fn"]
node = SubResource("BBNode_qaqnn")
method = &"throw_ninja_star"

[sub_resource type="BTSequence" id="BTSequence_ws7nq"]
children = [SubResource("BTAction_n0rxm"), SubResource("BTPlayAnimation_iiei3"), SubResource("BTAction_g2up4"), SubResource("BTAction_d5lkr"), SubResource("BTPlayAnimation_40yja"), SubResource("BTWait_2dc1v"), SubResource("BTPlayAnimation_qnpjq"), SubResource("BTCallMethod_yd0fn")]

[sub_resource type="BTProbability" id="BTProbability_omklt"]
children = [SubResource("BTSequence_ws7nq")]
run_chance = 0.25

[sub_resource type="BTCondition" id="BTCondition_d6aub"]
script = ExtResource("5_er18b")
distance_min = 200.0
distance_max = 10000.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_rpwld"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_dug2k"]
animation_player = SubResource("BBNode_rpwld")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_wc11r"]
script = ExtResource("7_ekws5")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BBNode" id="BBNode_aw5jj"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_3aihc"]
animation_player = SubResource("BBNode_aw5jj")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_1o163"]
duration = 0.5

[sub_resource type="BTSequence" id="BTSequence_vx4uy"]
children = [SubResource("BTCondition_d6aub"), SubResource("BTPlayAnimation_dug2k"), SubResource("BTAction_wc11r"), SubResource("BTPlayAnimation_3aihc"), SubResource("BTWait_1o163")]

[sub_resource type="BTSelector" id="BTSelector_ddhoo"]
children = [SubResource("BTRunLimit_60b8b"), SubResource("BTCooldown_3tvjt"), SubResource("BTSequence_cgwor"), SubResource("BTProbability_omklt"), SubResource("BTSequence_vx4uy")]

[resource]
description = "And for the final touch, we've added throwing ninja stars to our previous behavior. The [dec]Probability[/dec] decorator adds a little variance so that we don't throw ninja stars all the time. This decorator executes its child task with a certain probability, so when it's not 100%, sometimes it will return [FAILURE] instead. Some of the work in this [comp]Sequence[/comp] is done by actions you've seen already, and the [act]CallMethod[/act] action invokes the [color=cyan]throw_ninja_star[/color] method on the agent node. By the way, the agent is always the owner of the BTPlayer node, which executes this behavior tree.

This concludes our tutorial. Hopefully, this little endeavor has been useful to you. You can learn more by reading the [b]Online Documentation[/b], which covers topics like creating your own tasks. Also, check out the [b]Showcase[/b], which presents behaviors of agents made for this little demo. Simply click \"End Tutorial\" to return to the [b]Showcase[/b]."
blackboard_plan = SubResource("BlackboardPlan_ewfwq")
root_task = SubResource("BTSelector_ddhoo")
