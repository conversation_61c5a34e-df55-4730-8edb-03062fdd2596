; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="AntsInMyJam"
config/features=PackedStringArray("4.4", "C#", "Forward Plus")

[dotnet]

project/assembly_name="AntsInMyJam"

[shader_globals]

WORLD_HORIZON_COLOUR={
"type": "color",
"value": Color(0.258824, 0.839216, 0.839216, 1)
}
WORLD_LIGHT={
"type": "color",
"value": Color(1, 1, 0.901961, 1)
}
WORLD_SKY_COLOUR={
"type": "color",
"value": Color(0.0784314, 0.411765, 0.768627, 1)
}
WORLD_CLOUD_ENERGY={
"type": "float",
"value": 0.5
}
WORLD_CLOUD_VIBRANCY={
"type": "float",
"value": 1.33
}
