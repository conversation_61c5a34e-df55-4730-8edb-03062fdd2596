[gd_resource type="BehaviorTree" load_steps=21 format=3 uid="uid://bpdm5jnegi38"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_2jpsu"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="2_h5db5"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="3_bpmfp"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_46tbn"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_nrd4b"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qiw21"]
animation_player = SubResource("BBNode_nrd4b")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_xlud8"]
min_duration = 0.7
max_duration = 1.5

[sub_resource type="BTSequence" id="BTSequence_yhjh1"]
custom_name = "Pause before action"
children = [SubResource("BTPlayAnimation_qiw21"), SubResource("BTRandomWait_xlud8")]

[sub_resource type="BBNode" id="BBNode_wpj6d"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_olf37"]
animation_player = SubResource("BBNode_wpj6d")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_ulbrf"]
script = ExtResource("1_2jpsu")
group = &"player"
output_var = &"target"

[sub_resource type="BTAction" id="BTAction_a4jqi"]
script = ExtResource("2_h5db5")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BTTimeLimit" id="BTTimeLimit_xek5v"]
time_limit = 2.0
children = [SubResource("BTAction_a4jqi")]

[sub_resource type="BTSequence" id="BTSequence_1xfnq"]
custom_name = "Pursue player"
children = [SubResource("BTPlayAnimation_olf37"), SubResource("BTAction_ulbrf"), SubResource("BTTimeLimit_xek5v")]

[sub_resource type="BTAction" id="BTAction_kidxn"]
script = ExtResource("3_bpmfp")
target_var = &"target"

[sub_resource type="BTWait" id="BTWait_tadkc"]
duration = 0.1

[sub_resource type="BBNode" id="BBNode_s8evu"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_ppmxd"]
await_completion = 2.0
animation_player = SubResource("BBNode_s8evu")
animation_name = &"attack_1"

[sub_resource type="BTSequence" id="BTSequence_ww5v2"]
custom_name = "Melee attack"
children = [SubResource("BTAction_kidxn"), SubResource("BTWait_tadkc"), SubResource("BTPlayAnimation_ppmxd")]

[sub_resource type="BTSequence" id="BTSequence_pxl2k"]
children = [SubResource("BTSequence_yhjh1"), SubResource("BTSequence_1xfnq"), SubResource("BTSequence_ww5v2")]

[resource]
description = "Welcome to the [b]LimboAI[/b] demo project! This demo project consists of three parts:
- If you're new to behavior trees, you can access the [b]Tutorial[/b] by clicking the \"Begin Tutorial\" button on the toolbar.
- To experience the demo game, just click the \"Switch to Game\" button.
- On this screen, you can inspect the AI behavior tree in action for each agent in the game."
blackboard_plan = SubResource("BlackboardPlan_46tbn")
root_task = SubResource("BTSequence_pxl2k")
