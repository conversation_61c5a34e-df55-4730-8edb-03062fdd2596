@tool
extends BTAction

@export var own_team_var: StringName = &"team"

@export var shape_cast_param: BBNode

@export var target_var: StringName = &"target"

var shape_cast: ShapeCast2D:
	get:
		return shape_cast_param.get_value(scene_root, blackboard)

# Display a customized name (requires @tool).
func _generate_name() -> String:
	return "FindTarget: NOT %s" % [LimboUtility.decorate_var(own_team_var)]

func _enter() -> void:
	shape_cast.enabled = true

func _exit() -> void:
	shape_cast.enabled = false

func _tick(_delta: float) -> Status:
	var current_target = blackboard.get_var(target_var, null)
	if is_instance_valid(current_target):
		print("Current target is valid: ", current_target)
		return SUCCESS

	for contact in shape_cast.collision_result:
		var target = contact.collision_local_shape
		print("Inspecting target: ", target)
		if target.is_in_group("agents") or target.is_in_group("buildings"):
			if target.get(own_team_var, null) != blackboard.get_var(own_team_var, null):
				print("Found target: ", target)
				blackboard.set_var(target_var, target)
				return SUCCESS

	return FAILURE
