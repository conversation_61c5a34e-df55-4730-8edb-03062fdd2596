[gd_scene load_steps=5 format=3 uid="uid://ehc00upu3co"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_mbrnd"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_ttkri"]
[ext_resource type="BehaviorTree" uid="uid://beiki511huxb8" path="res://demo/ai/trees/tutorial/tutorial_06_decorators.tres" id="3_tpgll"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialDecorators" instance=ExtResource("1_mbrnd")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_ttkri")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_ttkri")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_ttkri")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_ttkri")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_ttkri")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_ttkri")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_tpgll")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
