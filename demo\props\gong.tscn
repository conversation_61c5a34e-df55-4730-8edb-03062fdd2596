[gd_scene load_steps=12 format=3 uid="uid://bpd1wmw2f7bvg"]

[ext_resource type="Script" uid="uid://d3cmj6skb7k07" path="res://demo/props/gong.gd" id="1_77c1i"]
[ext_resource type="Texture2D" uid="uid://i476iia1ua8q" path="res://demo/assets/env_gong.png" id="1_kbnv6"]
[ext_resource type="Texture2D" uid="uid://dwhhxj5557qrb" path="res://demo/assets/shadow.png" id="1_vl1mv"]
[ext_resource type="Texture2D" uid="uid://dj4oayt5ttvh8" path="res://demo/assets/fx.png" id="2_dib3m"]
[ext_resource type="Script" uid="uid://dq5h6yydckjc1" path="res://demo/agents/scripts/hurtbox.gd" id="5_xeb5y"]
[ext_resource type="Script" uid="uid://5byn62y1oa76" path="res://demo/agents/scripts/health.gd" id="6_6a3q1"]

[sub_resource type="Animation" id="Animation_i5ovs"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Gong:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Gong:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Gong:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("FX/WaveL1:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("FX/WaveL1:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("FX/WaveL1:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-65, -89)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("FX/WaveL1:rotation")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.57058]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("FX/WaveL1:scale")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.5, 0.5)]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("FX/WaverR1:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(70, -89)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("FX/WaverR1:rotation")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [4.71215]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("FX/WaverR1:scale")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.5, 0.5)]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("FX/WaverR1:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("FX/WaverR1:modulate")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("FX/WaveL2:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-65, -89)]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("FX/WaveL2:rotation")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.57058]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("FX/WaveL2:scale")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.5, 0.5)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("FX/WaverR2:position")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(70, -89)]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("FX/WaverR2:rotation")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [4.71215]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("FX/WaverR2:scale")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.5, 0.5)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("FX/WaveL3:position")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-65, -89)]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("FX/WaveL3:rotation")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.57058]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("FX/WaveL3:scale")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.5, 0.5)]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("FX/WaverR3:position")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(70, -89)]
}
tracks/23/type = "value"
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/path = NodePath("FX/WaverR3:rotation")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [4.71215]
}
tracks/24/type = "value"
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/path = NodePath("FX/WaverR3:scale")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.5, 0.5)]
}
tracks/25/type = "value"
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/path = NodePath("FX/WaveL2:visible")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/26/type = "value"
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/path = NodePath("FX/WaveL2:modulate")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1)]
}
tracks/27/type = "value"
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/path = NodePath("FX/WaverR2:visible")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/28/type = "value"
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/path = NodePath("FX/WaverR2:modulate")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1)]
}
tracks/29/type = "value"
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/path = NodePath("FX/WaveL3:visible")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/30/type = "value"
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/path = NodePath("FX/WaveL3:modulate")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1)]
}
tracks/31/type = "value"
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/path = NodePath("FX/WaverR3:visible")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/32/type = "value"
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/path = NodePath("FX/WaverR3:modulate")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1)]
}

[sub_resource type="Animation" id="Animation_ie54r"]
resource_name = "struck"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Gong:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Gong:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Gong:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1.1, 0.9), Vector2(0.9, 1.1), Vector2(1.08, 0.92), Vector2(0.92, 1.08), Vector2(1.06, 0.94), Vector2(0.94, 1.06), Vector2(1.04, 0.96), Vector2(0.96, 1.04), Vector2(1.02, 0.98), Vector2(0.98, 1.02), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("FX/WaveL1:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("FX/WaveL1:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1), Color(0.607843, 0.898039, 1, 0)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("FX/WaveL1:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-65, -89), Vector2(-165, -89)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("FX/WaveL1:rotation")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [1.57058, 1.57058]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("FX/WaveL1:scale")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(0.5, 0.5), Vector2(1, 1)]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("FX/WaverR1:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(70, -89), Vector2(170, -89)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("FX/WaverR1:rotation")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [4.71215, 4.71215]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("FX/WaverR1:scale")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(0.5, 0.5), Vector2(1, 1)]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("FX/WaverR1:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("FX/WaverR1:modulate")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0.1, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1), Color(0.607843, 0.898039, 1, 0)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("FX/WaveL2:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-65, -89), Vector2(-65, -89), Vector2(-165, -89)]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("FX/WaveL2:rotation")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [1.57058, 1.57058, 1.57058]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("FX/WaveL2:scale")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.5, 0.5), Vector2(0.5, 0.5), Vector2(1, 1)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("FX/WaverR2:position")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(70, -89), Vector2(70, -89), Vector2(170, -89)]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("FX/WaverR2:rotation")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [4.71215, 4.71215, 4.71215]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("FX/WaverR2:scale")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.5, 0.5), Vector2(0.5, 0.5), Vector2(1, 1)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("FX/WaveL3:position")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.4, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-65, -89), Vector2(-65, -89), Vector2(-165, -89)]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("FX/WaveL3:rotation")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [1.57058, 1.57058]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("FX/WaveL3:scale")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.4, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.5, 0.5), Vector2(0.5, 0.5), Vector2(1, 1)]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("FX/WaverR3:position")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 0.4, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(70, -89), Vector2(70, -89), Vector2(170, -89)]
}
tracks/23/type = "value"
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/path = NodePath("FX/WaverR3:rotation")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [4.71215, 4.71215]
}
tracks/24/type = "value"
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/path = NodePath("FX/WaverR3:scale")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = {
"times": PackedFloat32Array(0, 0.4, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.5, 0.5), Vector2(0.5, 0.5), Vector2(1, 1)]
}
tracks/25/type = "value"
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/path = NodePath("FX/WaveL2:visible")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, true]
}
tracks/26/type = "value"
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/path = NodePath("FX/WaveL2:modulate")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1), Color(0.329412, 0.352941, 0.392157, 1), Color(0.607843, 0.898039, 1, 0)]
}
tracks/27/type = "value"
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/path = NodePath("FX/WaverR2:visible")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, true]
}
tracks/28/type = "value"
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/path = NodePath("FX/WaverR2:modulate")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = {
"times": PackedFloat32Array(0, 0.2, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1), Color(0.329412, 0.352941, 0.392157, 1), Color(0.607843, 0.898039, 1, 0)]
}
tracks/29/type = "value"
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/path = NodePath("FX/WaveL3:visible")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, true]
}
tracks/30/type = "value"
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/path = NodePath("FX/WaveL3:modulate")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = {
"times": PackedFloat32Array(-0.00147765, 0.4, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1), Color(0.329412, 0.352941, 0.392157, 1), Color(0.607843, 0.898039, 1, 0)]
}
tracks/31/type = "value"
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/path = NodePath("FX/WaverR3:visible")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, true]
}
tracks/32/type = "value"
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/path = NodePath("FX/WaverR3:modulate")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = {
"times": PackedFloat32Array(-0.00147765, 0.4, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(0.329412, 0.352941, 0.392157, 1), Color(0.329412, 0.352941, 0.392157, 1), Color(0.607843, 0.898039, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_332s2"]
_data = {
&"RESET": SubResource("Animation_i5ovs"),
&"struck": SubResource("Animation_ie54r")
}

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_nixvt"]
radius = 20.91
height = 186.37

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_ru7jf"]
radius = 39.2
height = 253.42

[node name="Gong" type="StaticBody2D"]
script = ExtResource("1_77c1i")
metadata/_edit_horizontal_guides_ = [111.0]

[node name="Root" type="Node2D" parent="."]
metadata/_edit_lock_ = true

[node name="Shadow" type="Sprite2D" parent="Root"]
modulate = Color(1, 1, 1, 0.329412)
position = Vector2(0, -8)
scale = Vector2(2.31, 1.7)
texture = ExtResource("1_vl1mv")

[node name="Gong" type="Sprite2D" parent="Root"]
texture = ExtResource("1_kbnv6")
offset = Vector2(0, -110)

[node name="FX" type="Node2D" parent="."]
metadata/_edit_lock_ = true

[node name="WaveL1" type="Sprite2D" parent="FX"]
visible = false
modulate = Color(0.329412, 0.352941, 0.392157, 1)
position = Vector2(-65, -89)
rotation = 1.57058
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_dib3m")
region_enabled = true
region_rect = Rect2(0, 0, 191, 74)

[node name="WaverR1" type="Sprite2D" parent="FX"]
visible = false
modulate = Color(0.329412, 0.352941, 0.392157, 1)
position = Vector2(70, -89)
rotation = 4.71215
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_dib3m")
region_enabled = true
region_rect = Rect2(0, 0, 191, 74)

[node name="WaveL2" type="Sprite2D" parent="FX"]
visible = false
modulate = Color(0.329412, 0.352941, 0.392157, 1)
position = Vector2(-65, -89)
rotation = 1.57058
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_dib3m")
region_enabled = true
region_rect = Rect2(0, 0, 191, 74)

[node name="WaverR2" type="Sprite2D" parent="FX"]
visible = false
modulate = Color(0.329412, 0.352941, 0.392157, 1)
position = Vector2(70, -89)
rotation = 4.71215
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_dib3m")
region_enabled = true
region_rect = Rect2(0, 0, 191, 74)

[node name="WaveL3" type="Sprite2D" parent="FX"]
visible = false
modulate = Color(0.329412, 0.352941, 0.392157, 1)
position = Vector2(-65, -89)
rotation = 1.57058
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_dib3m")
region_enabled = true
region_rect = Rect2(0, 0, 191, 74)

[node name="WaverR3" type="Sprite2D" parent="FX"]
visible = false
modulate = Color(0.329412, 0.352941, 0.392157, 1)
position = Vector2(70, -89)
rotation = 4.71215
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_dib3m")
region_enabled = true
region_rect = Rect2(0, 0, 191, 74)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_332s2")
}

[node name="GongSFX" type="AudioStreamPlayer" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0, -14.395)
rotation = 1.5708
shape = SubResource("CapsuleShape2D_nixvt")

[node name="Hurtbox" type="Area2D" parent="." node_paths=PackedStringArray("health")]
collision_layer = 8
collision_mask = 0
script = ExtResource("5_xeb5y")
health = NodePath("../Health")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="Hurtbox"]
position = Vector2(0, -14.395)
rotation = 1.5708
shape = SubResource("CapsuleShape2D_ru7jf")
debug_color = Color(0.886275, 0.109804, 0.756863, 0.419608)

[node name="Health" type="Node" parent="."]
script = ExtResource("6_6a3q1")
max_health = 1e+11

[connection signal="damaged" from="Health" to="." method="_on_health_damaged"]
