[gd_resource type="BehaviorTree" load_steps=36 format=3 uid="uid://tep50j4d6kgp"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_f605f"]
[ext_resource type="Script" uid="uid://b7v2utjmtge0x" path="res://demo/ai/tasks/in_range.gd" id="2_mj1cj"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="3_86p0r"]
[ext_resource type="Script" uid="uid://bi5e8366xi5s5" path="res://demo/ai/tasks/back_away.gd" id="4_u0vk1"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="6_1yikm"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_ewfwq"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BTAction" id="BTAction_2murg"]
script = ExtResource("1_f605f")
group = &"player"
output_var = &"target"

[sub_resource type="BTRunLimit" id="BTRunLimit_60b8b"]
children = [SubResource("BTAction_2murg")]

[sub_resource type="BTCondition" id="BTCondition_m15aa"]
script = ExtResource("2_mj1cj")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BTAction" id="BTAction_oc76s"]
script = ExtResource("3_86p0r")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_6d0yy"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_wsspf"]
await_completion = 5.0
animation_player = SubResource("BBNode_6d0yy")
animation_name = &"attack_1"

[sub_resource type="BBNode" id="BBNode_w45kn"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_d2lad"]
await_completion = 5.0
animation_player = SubResource("BBNode_w45kn")
animation_name = &"attack_2"

[sub_resource type="BTSequence" id="BTSequence_e0f8v"]
children = [SubResource("BTCondition_m15aa"), SubResource("BTAction_oc76s"), SubResource("BTPlayAnimation_wsspf"), SubResource("BTPlayAnimation_d2lad")]

[sub_resource type="BTCooldown" id="BTCooldown_3tvjt"]
children = [SubResource("BTSequence_e0f8v")]
duration = 5.0

[sub_resource type="BTCondition" id="BTCondition_x0uu7"]
script = ExtResource("2_mj1cj")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_wksgd"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_umlwj"]
animation_player = SubResource("BBNode_wksgd")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_6q0k4"]
script = ExtResource("4_u0vk1")
speed_var = &"speed"
max_angle_deviation = 0.7

[sub_resource type="BTTimeLimit" id="BTTimeLimit_6eii7"]
children = [SubResource("BTAction_6q0k4")]
time_limit = 2.0

[sub_resource type="BTAlwaysSucceed" id="BTAlwaysSucceed_ieord"]
children = [SubResource("BTTimeLimit_6eii7")]

[sub_resource type="BBNode" id="BBNode_ayt56"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_l1tdg"]
animation_player = SubResource("BBNode_ayt56")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_hh8ys"]
duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_cgwor"]
children = [SubResource("BTCondition_x0uu7"), SubResource("BTPlayAnimation_umlwj"), SubResource("BTAlwaysSucceed_ieord"), SubResource("BTPlayAnimation_l1tdg"), SubResource("BTWait_hh8ys")]

[sub_resource type="BTCondition" id="BTCondition_d6aub"]
script = ExtResource("2_mj1cj")
distance_min = 200.0
distance_max = 10000.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_rpwld"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_dug2k"]
animation_player = SubResource("BBNode_rpwld")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_wc11r"]
script = ExtResource("6_1yikm")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BBNode" id="BBNode_aw5jj"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_3aihc"]
animation_player = SubResource("BBNode_aw5jj")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_1o163"]
duration = 0.5

[sub_resource type="BTSequence" id="BTSequence_vx4uy"]
children = [SubResource("BTCondition_d6aub"), SubResource("BTPlayAnimation_dug2k"), SubResource("BTAction_wc11r"), SubResource("BTPlayAnimation_3aihc"), SubResource("BTWait_1o163")]

[sub_resource type="BTSelector" id="BTSelector_ddhoo"]
children = [SubResource("BTRunLimit_60b8b"), SubResource("BTCooldown_3tvjt"), SubResource("BTSequence_cgwor"), SubResource("BTSequence_vx4uy")]

[resource]
description = "Let's explore some other [dec]Decorators[/dec]. Here's a slightly improved version of the previous behavior. We incorporate the [dec]TimeLimit[/dec] decorator with the [act]Back Away[/act] action to achieve a similar effect as before (walking away from the target). The TimeLimit decorator ensures that the child task does not take too long to finish its work. If the time runs out, the task's execution will be aborted, and TimeLimit will return [FAILURE].

Another useful decorator is [dec]AlwaysSucceed[/dec], which converts any [FAILURE] returned by its child into [SUCCESS]. We use this decorator here because TimeLimit may result in [FAILURE], but we still want the [comp]Sequence[/comp] to continue. Also, there are similar decorators such as [dec]AlwaysFail[/dec] and [dec]Invert[/dec], which you can use in your behavior trees as needed.

We also add a second attack, so that Junior feels more badass now. We've got a little hit-and-run behavior going on here!"
blackboard_plan = SubResource("BlackboardPlan_ewfwq")
root_task = SubResource("BTSelector_ddhoo")
