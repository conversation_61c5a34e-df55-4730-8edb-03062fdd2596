class_name AreaCamera extends Node2D

## The camera node to control
@export var camera: Camera2D

## Threshold in pixels from screen edge to start moving the camera
@export var threshold: Vector2 = Vector2(200, 150)

# Reference to the Map node
var map: Node

## Maximum camera movement speed
@export var max_speed: float = 500.0

## Curve to determine camera speed based on mouse position (0-1 range)
@export var speed_curve: Curve

## Time in seconds to reach maximum speed
@export var acceleration_time: float = 1.0

## Whether the camera movement is active
@export var active: bool = true

var _viewport_size: Vector2
var _camera_position: Vector2

# Timers for each edge to track how long the mouse has been in the threshold
var _left_timer: float = 0.0
var _right_timer: float = 0.0
var _top_timer: float = 0.0
var _bottom_timer: float = 0.0

func _ready() -> void:
	if camera == null:
		push_warning("AreaCamera: No camera assigned")

	_viewport_size = get_viewport().get_visible_rect().size
	if camera:
		_camera_position = camera.position

	# Get the Map node from the root of the scene
	map = get_tree().get_root().get_node_or_null("World/Map")
	if not map:
		push_warning("AreaCamera: Map node not found at root of scene")

func _process(delta: float) -> void:

	if not active or camera == null:
		return

	_viewport_size = get_viewport().get_visible_rect().size
	var mouse_pos = get_viewport().get_mouse_position()

	# Calculate normalized position within threshold for each edge
	var left_factor = 0.0
	var right_factor = 0.0
	var top_factor = 0.0
	var bottom_factor = 0.0

	# Check left edge
	if mouse_pos.x < threshold.x:
		left_factor = 1.0 - (mouse_pos.x / threshold.x)
		_left_timer = min(_left_timer + delta, acceleration_time)
	else:
		_left_timer = 0.0

	# Check right edge
	if mouse_pos.x > _viewport_size.x - threshold.x:
		right_factor = (mouse_pos.x - (_viewport_size.x - threshold.x)) / threshold.x
		_right_timer = min(_right_timer + delta, acceleration_time)
	else:
		_right_timer = 0.0

	# Check top edge
	if mouse_pos.y < threshold.y:
		top_factor = 1.0 - (mouse_pos.y / threshold.y)
		_top_timer = min(_top_timer + delta, acceleration_time)
	else:
		_top_timer = 0.0

	# Check bottom edge
	if mouse_pos.y > _viewport_size.y - threshold.y:
		bottom_factor = (mouse_pos.y - (_viewport_size.y - threshold.y)) / threshold.y
		_bottom_timer = min(_bottom_timer + delta, acceleration_time)
	else:
		_bottom_timer = 0.0

	# Calculate movement direction and speed
	var move_direction = Vector2.ZERO

	# Calculate time-based acceleration factors (0 to 1)
	var left_accel_factor = _left_timer / acceleration_time
	var right_accel_factor = _right_timer / acceleration_time
	var top_accel_factor = _top_timer / acceleration_time
	var bottom_accel_factor = _bottom_timer / acceleration_time

	if left_factor > 0:
		move_direction.x = -get_speed_from_curve(left_factor) * left_accel_factor
	elif right_factor > 0:
		move_direction.x = get_speed_from_curve(right_factor) * right_accel_factor

	if top_factor > 0:
		move_direction.y = -get_speed_from_curve(top_factor) * top_accel_factor
	elif bottom_factor > 0:
		move_direction.y = get_speed_from_curve(bottom_factor) * bottom_accel_factor

	# Apply movement
	if move_direction != Vector2.ZERO:
		_camera_position += move_direction * delta * max_speed

		# Get half viewport size for constraint calculations
		var half_viewport = _viewport_size * 0.5 * camera.zoom

		# Apply constraints based on camera corners, not center
		if map:
			_camera_position.x = clamp(_camera_position.x,
				map.max_left + half_viewport.x,
				map.max_right - half_viewport.x)
			_camera_position.y = clamp(_camera_position.y,
				map.max_top + half_viewport.y,
				map.max_bottom - half_viewport.y)

		camera.position = _camera_position

## Get speed factor from curve based on position within threshold
func get_speed_from_curve(factor: float) -> float:
	if speed_curve:
		return speed_curve.sample(factor)
	return factor
