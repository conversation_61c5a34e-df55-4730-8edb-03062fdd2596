[gd_resource type="BehaviorTree" load_steps=35 format=3 uid="uid://cpxk7jnqpwwlc"]

[ext_resource type="Script" uid="uid://bicxffqmm7ek" path="res://demo/ai/tasks/select_random_nearby_pos.gd" id="1_sgn0p"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="2_pshl2"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_58oq1"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 300.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"
var/summon_cooldown/name = &"summon_cooldown"
var/summon_cooldown/type = 1
var/summon_cooldown/value = false
var/summon_cooldown/hint = 0
var/summon_cooldown/hint_string = ""

[sub_resource type="BBNode" id="BBNode_b7euf"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_sot6a"]
animation_player = SubResource("BBNode_b7euf")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_0wc6p"]

[sub_resource type="BTSequence" id="BTSequence_kwkxc"]
custom_name = "Summoning sickness"
children = [SubResource("BTPlayAnimation_sot6a"), SubResource("BTRandomWait_0wc6p")]

[sub_resource type="BTRunLimit" id="BTRunLimit_x2jr4"]
children = [SubResource("BTSequence_kwkxc")]

[sub_resource type="BBVariant" id="BBVariant_y3x7n"]
type = 2
saved_value = 6
resource_name = "6"

[sub_resource type="BTCheckAgentProperty" id="BTCheckAgentProperty_olmdj"]
property = &"summon_count"
check_type = 1
value = SubResource("BBVariant_y3x7n")

[sub_resource type="BTComment" id="BTComment_ef6y0"]
custom_name = "Variable $summon_cooldown is updated by the Cooldown task below"

[sub_resource type="BBVariant" id="BBVariant_hgde2"]
type = 1
saved_value = false
resource_name = "false"

[sub_resource type="BTCheckVar" id="BTCheckVar_hc3o3"]
variable = &"summon_cooldown"
value = SubResource("BBVariant_hgde2")
children = [SubResource("BTComment_ef6y0")]

[sub_resource type="BBNode" id="BBNode_q8bei"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_6leni"]
animation_player = SubResource("BBNode_q8bei")
animation_name = &"summon"
blend = 0.1

[sub_resource type="BTWait" id="BTWait_8og18"]
duration = 0.5

[sub_resource type="BTAction" id="BTAction_yeyns"]
script = ExtResource("1_sgn0p")
range_min = 300.0
range_max = 500.0
position_var = &"minion_pos"

[sub_resource type="BBVariant" id="BBVariant_wfjwy"]
type = 5
value_source = 1
variable = &"minion_pos"
resource_name = "$minion_pos"

[sub_resource type="BBNode" id="BBNode_v271m"]
saved_value = NodePath(".")
resource_name = "."

[sub_resource type="BTCallMethod" id="BTCallMethod_4ath5"]
node = SubResource("BBNode_v271m")
method = &"summon_minion"
args = Array[BBVariant]([SubResource("BBVariant_wfjwy")])

[sub_resource type="BTCooldown" id="BTCooldown_25f70"]
duration = 6.0
cooldown_state_var = &"summon_cooldown"
children = [SubResource("BTCallMethod_4ath5")]

[sub_resource type="BTWait" id="BTWait_tdhfn"]

[sub_resource type="BTSequence" id="BTSequence_2n742"]
custom_name = "Summon minion"
children = [SubResource("BTCheckAgentProperty_olmdj"), SubResource("BTCheckVar_hc3o3"), SubResource("BTPlayAnimation_6leni"), SubResource("BTWait_8og18"), SubResource("BTAction_yeyns"), SubResource("BTCooldown_25f70"), SubResource("BTWait_tdhfn")]

[sub_resource type="BTAction" id="BTAction_33wje"]
script = ExtResource("1_sgn0p")
range_min = 300.0
range_max = 700.0
position_var = &"pos"

[sub_resource type="BBNode" id="BBNode_w5fh2"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qbxm8"]
animation_player = SubResource("BBNode_w5fh2")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_dfifw"]
script = ExtResource("2_pshl2")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTTimeLimit" id="BTTimeLimit_i05cm"]
time_limit = 3.0
children = [SubResource("BTAction_dfifw")]

[sub_resource type="BBNode" id="BBNode_vinxt"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_t7drr"]
animation_player = SubResource("BBNode_vinxt")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_axjfh"]

[sub_resource type="BTSequence" id="BTSequence_ks71q"]
custom_name = "Chill"
children = [SubResource("BTPlayAnimation_t7drr"), SubResource("BTRandomWait_axjfh")]

[sub_resource type="BTSequence" id="BTSequence_dss4d"]
custom_name = "Reposition"
children = [SubResource("BTAction_33wje"), SubResource("BTPlayAnimation_qbxm8"), SubResource("BTTimeLimit_i05cm"), SubResource("BTSequence_ks71q")]

[sub_resource type="BTSelector" id="BTSelector_gp6q5"]
children = [SubResource("BTRunLimit_x2jr4"), SubResource("BTSequence_2n742"), SubResource("BTSequence_dss4d")]

[resource]
description = "[con]CheckAgentProperty[/con] examines the agent’s property value and compares it to a given value or a blackboard variable. In this example, it checks if the [color=cyan]summon_count[/color] property is less than 6.
You can also check or share a [dec]Cooldown[/dec] in different parts of the behavior tree if you provide a blackboard variable for the [dec]Cooldown[/dec] decorator to use. [con]CheckVar[/con] is responsible here for examining the cooldown variable named \"summon_cooldown\"."
blackboard_plan = SubResource("BlackboardPlan_58oq1")
root_task = SubResource("BTSelector_gp6q5")
