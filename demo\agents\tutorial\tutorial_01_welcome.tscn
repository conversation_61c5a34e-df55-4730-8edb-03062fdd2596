[gd_scene load_steps=5 format=3 uid="uid://dk67yawiu33jv"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_2vrmp"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_3h4dj"]
[ext_resource type="BehaviorTree" uid="uid://b1mfh8yad7rmw" path="res://demo/ai/trees/tutorial/tutorial_01_welcome.tres" id="3_ilmgw"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialWelcome" instance=ExtResource("1_2vrmp")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_3h4dj")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_3h4dj")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_3h4dj")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_3h4dj")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_3h4dj")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_3h4dj")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_ilmgw")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
