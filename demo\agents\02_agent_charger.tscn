[gd_scene load_steps=5 format=3 uid="uid://g1pnvanyxcpa"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_n83hi"]
[ext_resource type="Texture2D" uid="uid://ce16nc0wy2s8" path="res://demo/assets/agent_charger.png" id="2_y7pic"]
[ext_resource type="BehaviorTree" uid="uid://ylife72ym5et" path="res://demo/ai/trees/02_agent_charger.tres" id="3_nacc3"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_pmyhf"]

[node name="AgentCharger" instance=ExtResource("1_n83hi")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_y7pic")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_y7pic")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_y7pic")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_y7pic")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_y7pic")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_y7pic")

[node name="Hitbox" parent="Root" index="1"]
damage = 2.0

[node name="Health" parent="." index="3"]
max_health = 8.0

[node name="BTPlayer" type="BTPlayer" parent="." index="4"]
behavior_tree = ExtResource("3_nacc3")
blackboard_plan = SubResource("BlackboardPlan_pmyhf")
