[gd_resource type="BehaviorTree" load_steps=30 format=3 uid="uid://c2bxoo68ywb27"]

[ext_resource type="Script" uid="uid://bicxffqmm7ek" path="res://demo/ai/tasks/select_random_nearby_pos.gd" id="1_cdtqu"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="2_31fsn"]
[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="3_y1r1a"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="4_jlgat"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="5_o4ggh"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_46tbn"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_3y70b"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_klk26"]
animation_player = SubResource("BBNode_3y70b")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_chmiy"]
script = ExtResource("1_cdtqu")
range_min = 200.0
range_max = 500.0
position_var = &"pos"

[sub_resource type="BTAction" id="BTAction_5kivl"]
script = ExtResource("2_31fsn")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTSequence" id="BTSequence_k184c"]
custom_name = "Chaotic Walk"
children = [SubResource("BTPlayAnimation_klk26"), SubResource("BTAction_chmiy"), SubResource("BTAction_5kivl")]

[sub_resource type="BTProbability" id="BTProbability_ifsry"]
children = [SubResource("BTSequence_k184c")]

[sub_resource type="BBNode" id="BBNode_nrd4b"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qiw21"]
animation_player = SubResource("BBNode_nrd4b")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_xlud8"]
min_duration = 0.7
max_duration = 1.5

[sub_resource type="BTSequence" id="BTSequence_n5ltc"]
custom_name = "Pause before action"
children = [SubResource("BTPlayAnimation_qiw21"), SubResource("BTRandomWait_xlud8")]

[sub_resource type="BBNode" id="BBNode_wpj6d"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_olf37"]
animation_player = SubResource("BBNode_wpj6d")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_ulbrf"]
script = ExtResource("3_y1r1a")
group = &"player"
output_var = &"target"

[sub_resource type="BTAction" id="BTAction_a4jqi"]
script = ExtResource("4_jlgat")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BTTimeLimit" id="BTTimeLimit_xek5v"]
time_limit = 2.0
children = [SubResource("BTAction_a4jqi")]

[sub_resource type="BTSequence" id="BTSequence_1xfnq"]
custom_name = "Pursue player"
children = [SubResource("BTPlayAnimation_olf37"), SubResource("BTAction_ulbrf"), SubResource("BTTimeLimit_xek5v")]

[sub_resource type="BTAction" id="BTAction_kidxn"]
script = ExtResource("5_o4ggh")
target_var = &"target"

[sub_resource type="BTWait" id="BTWait_tadkc"]
duration = 0.1

[sub_resource type="BBNode" id="BBNode_s8evu"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_ppmxd"]
await_completion = 2.0
animation_player = SubResource("BBNode_s8evu")
animation_name = &"attack_1"

[sub_resource type="BTSequence" id="BTSequence_ww5v2"]
custom_name = "Melee attack"
children = [SubResource("BTAction_kidxn"), SubResource("BTWait_tadkc"), SubResource("BTPlayAnimation_ppmxd")]

[sub_resource type="BTSequence" id="BTSequence_pxl2k"]
custom_name = "Engage player"
children = [SubResource("BTSequence_n5ltc"), SubResource("BTSequence_1xfnq"), SubResource("BTSequence_ww5v2")]

[sub_resource type="BTSelector" id="BTSelector_y3llm"]
children = [SubResource("BTProbability_ifsry"), SubResource("BTSequence_pxl2k")]

[resource]
description = "Here, the [dec]Probability[/dec] decorator allows the execution of its branch in 50% of cases. This introduces a slight variation to the behavior each time the [comp]Chaotic walk[/comp] sequence is considered by the [comp]Selector[/comp]. This sequence can be selected several times in a row as long as the Probability decorator permits it. The [comp]Engage player[/comp] sequence is only executed if Probability returns [FAILURE]."
blackboard_plan = SubResource("BlackboardPlan_46tbn")
root_task = SubResource("BTSelector_y3llm")
