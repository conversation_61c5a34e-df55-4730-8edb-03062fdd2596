[gd_scene load_steps=5 format=3 uid="uid://bx4iabilsv3rs"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_p8nwq"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_hnwhw"]
[ext_resource type="BehaviorTree" uid="uid://cb0ybf24ahnc3" path="res://demo/ai/trees/tutorial/tutorial_03_types.tres" id="3_a31ka"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialTypes" instance=ExtResource("1_p8nwq")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_hnwhw")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_hnwhw")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_hnwhw")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_hnwhw")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_hnwhw")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_hnwhw")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_a31ka")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
