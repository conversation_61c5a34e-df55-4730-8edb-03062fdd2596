[gd_scene load_steps=5 format=3 uid="uid://730bthc0ywhd"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_13xup"]
[ext_resource type="Texture2D" uid="uid://h70okn6hmxum" path="res://demo/assets/agent_imp.png" id="2_xd5s8"]
[ext_resource type="BehaviorTree" uid="uid://c2bxoo68ywb27" path="res://demo/ai/trees/03_agent_imp.tres" id="3_furh3"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_aog23"]

[node name="AgentImp" instance=ExtResource("1_13xup")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_xd5s8")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_xd5s8")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_xd5s8")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_xd5s8")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_xd5s8")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_xd5s8")

[node name="Health" parent="." index="3"]
max_health = 4.0

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_furh3")
blackboard_plan = SubResource("BlackboardPlan_aog23")
