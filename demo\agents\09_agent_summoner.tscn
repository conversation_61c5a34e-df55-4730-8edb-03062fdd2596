[gd_scene load_steps=5 format=3 uid="uid://bycvi2fb0f7ue"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_neifu"]
[ext_resource type="Texture2D" uid="uid://ombnpn2edldt" path="res://demo/assets/agent_summoner.png" id="2_fcti4"]
[ext_resource type="BehaviorTree" uid="uid://cpxk7jnqpwwlc" path="res://demo/ai/trees/09_agent_summoner.tres" id="3_bunpq"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_bqv3d"]

[node name="AgentSummoner" instance=ExtResource("1_neifu")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_fcti4")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_fcti4")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_fcti4")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_fcti4")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_fcti4")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_fcti4")

[node name="Health" parent="." index="3"]
max_health = 8.0

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_bunpq")
blackboard_plan = SubResource("BlackboardPlan_bqv3d")
